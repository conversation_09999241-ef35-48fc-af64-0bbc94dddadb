import ArgumentParser
import Foundation
import PathKit
import Yams

// MARK: - OGDeploymentGenerator

// # OGDeploymentGenerator
//
// ## Overview
//
// The `OGDeploymentGenerator` is a comprehensive command-line tool that automates the creation and configuration of iOS app projects with complete CI/CD pipeline setup. It serves as the central orchestrator for generating Xcode projects, deployment configurations, and build automation scripts from declarative YAML configuration files.
//
// ### Purpose and Responsibilities
//
// - **Project Generation**: Creates complete Xcode projects using XcodeGen with proper target configurations, schemes, and build settings
// - **Deployment Automation**: Generates GitHub Actions workflows and Fastlane configurations for automated testing, building, and deployment
// - **Package Management**: Automatically discovers and integrates local Swift packages and external dependencies
// - **Multi-Target Support**: Handles multiple app targets with different configurations (Alpha, Beta, Release) from a single source
// - **Firebase Integration**: Manages Firebase configurations across different environments and validates bundle identifiers
// - **Build Script Management**: Configures pre-build and post-build scripts for secrets generation, UI catalog creation, and deployment tasks
//
// ### Key Concepts and Terminology
//
// - **Apps.yml**: Central configuration file defining all app targets, their properties, and build configurations
// - **Target**: An individual app or framework within the project (e.g., "MyApp", "MyApp-Tests", "MyApp-NotificationService")
// - **Configuration**: Build environment variants (Alpha, Beta, Release) with different settings and certificates
// - **Templates**: Stencil template files used to generate deployment scripts and configuration files
// - **Shared Framework**: Optional architecture where common code is built as a framework linked by all apps
// - **Package Discovery**: Automatic detection and integration of local Swift packages from specified directories
//
// ### Integration with Build Process
//
// The tool integrates into the development workflow as follows:
//
// 1. **Configuration Phase**: Developers define apps and settings in YAML files
// 2. **Generation Phase**: Tool generates Xcode project files, CI/CD workflows, and deployment scripts
// 3. **Build Phase**: Xcode builds the generated project with integrated packages and dependencies
// 4. **Deployment Phase**: Generated Fastlane and GitHub Actions handle automated testing and deployment
//
// ### Architecture Overview
//
// The generator follows a pipeline architecture with distinct phases:
//
// ```
// Input YAML Files → Data Processing → Template Rendering → File Generation → Project Creation
// ```
//
// - **Data Layer**: Models representing project structure, targets, dependencies, and configurations
// - **Processing Layer**: Logic for merging configurations, discovering packages, and validating settings
// - **Generation Layer**: Template rendering and file creation using Stencil templating engine
// - **Integration Layer**: XcodeGen integration and project opening
//
// ## Usage Examples
//
// ### Basic Usage
// ```bash
// og-deployment-generator --project-path /path/to/project --apps-yml-path MyApps.yml
// ```
//
// ### Advanced Configuration
// ```bash
// og-deployment-generator \
//  --project-path /path/to/project \
//  --apps-yml-path config/Apps.yml \
//  --templates-dir CustomTemplates \
//  --use-framework \
//  --target MySpecificApp \
//  --xcode-version 16.0 \
//  --ios-version 15.0
// ```
//
// ## Extension Points
//
// - **Custom Templates**: Provide custom Stencil templates via `--templates-dir`
// - **Build Scripts**: Extend pre-build and post-build script options
// - **Package Discovery**: Add new package paths for automatic discovery
// - **Configuration Validation**: Extend Firebase and bundle ID validation logic
//
// ## Common Patterns
//
// - **Singleton Pattern**: `FileGenerator.shared` and `DefaultNames.shared` for global state management
// - **Builder Pattern**: Default configuration builders for consistent object creation
// - **Pipeline Pattern**: Sequential data processing through transformation methods
// - **Template Method Pattern**: Consistent YAML encoding/decoding across different data types

@main
struct OGDeploymentGenerator: ParsableCommand {
  // MARK: - Command Configuration

  /// Command-line interface configuration defining the tool's identity and behavior
  static let configuration = CommandConfiguration(
    commandName: "og-deployment-generator",
    abstract: "A tool for generating deployment configurations for iOS projects.",
    version: "2.0.0"
  )

  // MARK: - Core Configuration Options

  /// These options define the fundamental project structure and file locations
  @Option(help: "Absolute path to the root directory of your iOS project. This is where the tool will generate all project files and look for configuration files. Default: current working directory.") private var projectPath: String = FileManager.default.currentDirectoryPath

  @Option(help: "Relative path from project root to the Apps.yml configuration file that defines your app targets and their build configurations. Example: 'config/MyApps.yml'.") private var appsYmlPath: String = "OGAppKitApps.yml"

  @Option(name: .long, help: "Relative path from project root to a custom templates directory containing Stencil template files (Appfile.rb.stencil, Fastfile.rb.stencil, main.yml.stencil). If not specified, built-in default templates are used. Example: 'CustomTemplates'.") private var templatesDir: String?

  @Option(name: .long, help: "Relative path from project root to the directory where GitHub Actions workflow files will be generated. The tool creates CI/CD workflows for building and deploying your apps.") private var githubWorkflowsDir: String = ".github/workflows"

  @Option(name: .long, help: "Relative path from project root to the directory where Fastlane configuration files (Appfile, Fastfile) will be generated. Fastlane automates iOS app building, testing, and deployment.") private var fastlaneDir: String = "fastlane"

  @Option(name: .long, help: "Relative path from project root to the directory containing your individual app target folders. Each app defined in Apps.yml will have its own subdirectory here.") private var appsDir: String = "Apps"

  @Option(name: .long, help: "Relative path from project root to the directory containing Firebase configuration files (GoogleService-Info.plist) organized by environment (alpha/beta/release).") private var firebaseInfoPlistDir: String = "Firebase"

  @Option(name: .long, help: "The name of your Xcode project. This will be used as the .xcodeproj filename and in various configuration files. Example: 'MyAwesomeApp'.") private var projectName: String = "OttoGroup"

  @Option(name: .long, help: "Relative path from project root to the .appiconset directory containing your app icon assets. Used for configuring app icon references in the generated project.") private var appIconSetPath: String = "Resources/Assets.xcassets/AppIcon.appiconset"

  @Option(name: .long, help: "Array of relative paths from project root to directories containing local Swift packages. These packages will be automatically discovered and added as dependencies. Specify multiple paths by repeating the option: --packages-path Packages --packages-path OGKit --packages-path Libraries.") private var packagesPath: [String] = ["Packages", "OGKit"]

  @Option(name: .long, help: "Xcode version to target for the generated project and CI/CD workflows. This affects build settings and GitHub Actions runner configuration. Format: 'X.Y' (e.g., '16.0').") private var xcodeVersion: String = "16.0"

  @Option(name: .long, help: "Minimum iOS deployment target version for your apps. This sets the IPHONEOS_DEPLOYMENT_TARGET build setting. Format: 'X.Y' (e.g., '15.0').") private var iosVersion: String = "15.0"

  @Option(name: .long, help: "Swift language version to use in the generated project. This sets the SWIFT_VERSION build setting. Format: 'X.Y' (e.g., '5.9').") private var swiftVersion: String = "5.9"

  @Option(name: .long, help: "Minimum required version of XcodeGen tool for generating the Xcode project. Ensures compatibility with project generation features. Format: 'X.Y.Z' (e.g., '2.44.1').") private var minimumXcodeGenVersion: String = "2.44.1"

  @Option(name: .long, help: "Method for generating and storing app secrets during build. 'keychain' uses macOS Keychain, 'keystore' uses encrypted file storage. Affects pre-build script selection. Options: keychain, keystore.") private var secretsGenerationType: SecretsGenerationType = .keystore

  @Option(name: .long, help: "Relative path from project root to a directory containing template files that should be copied to each new app target during initialization. Useful for providing default source files, resources, or configuration. Example: 'Templates/AppTemplate'.") private var initialFilesPath: String?

  @Option(name: .long, help: "Array of pre-build scripts to execute before compiling each app target. These run during Xcode's build phases. Available scripts: generateSecretsKeychain, generateSecretsKeystore, generateUICatalog.") private var preBuildScripts: [ScriptEnum] = [
    .generateSecretsKeychain,
    .generateUICatalog
  ]

  @Option(name: .long, help: "Array of post-build scripts to execute after compiling each app target. These run during Xcode's build phases for tasks like versioning and deployment preparation. Available scripts: updateBuildNumber, copyGoogleServiceInfoPlist, uploadSymbolsForCrashlytics.") private var postBuildScripts: [ScriptEnum] = [
    .updateBuildNumber,
    .copyGoogleServiceInfoPlist,
    .uploadSymbolsForCrashlytics
  ]
  @Option(name: .long, help: "Relative path from project root to the dependencies.yml file that defines external package dependencies and build tool plugins for your project. This file specifies Swift packages, SDKs, and other dependencies.") private var dependenciesYml: String = DefaultName.File.dependenciesYML

  @Option(name: .long, help: "Relative path from project root to the packages.yml file that defines local package configurations. This file maps local Swift packages to their paths and settings.") private var packagesYml: String = DefaultName.File.packagesYML

  @Option(name: .long, help: "Custom name for the shared framework target when using --use-framework. This framework contains shared code used by all app targets. Only relevant when --use-framework is enabled.") private var sharedFrameworkName: String?

  @Option(name: .long, help: "Build only a specific app target instead of all targets defined in Apps.yml. Useful for focused development or testing. Specify the target key from your Apps.yml file (e.g., 'WIT', 'ACME'). If not specified, all targets are built.") private var target: String?

  @Flag(name: .long, help: "Use a shared framework architecture instead of a shared source folder. When enabled, creates a framework target containing shared code that all apps link against. When disabled, uses a shared source directory that apps compile directly.") private var useFramework: Bool = false

  @Flag(name: .long, help: "Preserve intermediate YAML files generated during project creation (project.yml, base.yml, targets.yml). Useful for debugging project generation issues or understanding the XcodeGen configuration. Default: files are deleted after use.") private var keepGeneratedYML: Bool = false

  @Flag(name: .long, help: "Skip automatically opening the generated Xcode project after creation. By default, the tool opens the .xcodeproj file in Xcode when generation completes. Use this flag for automated scripts or CI environments.") private var doNotOpenProject: Bool = false

  @Flag(name: .long, help: "Generate a basic Swift App struct file (MyAppView.swift) for new app targets when --initial-files-path is not specified. Provides a minimal starting point for SwiftUI apps. Only applies to newly created app targets.") private var generateSwiftAppFile: Bool = false

  // MARK: - Main Execution Pipeline

  /// Main execution method that orchestrates the entire project generation process.
  ///
  /// This method follows a carefully designed pipeline that processes configuration data,
  /// generates intermediate files, and creates the final Xcode project with deployment configurations.
  ///
  /// ## Execution Flow
  ///
  /// 1. **Initialization**: Configure global state and copy binary dependencies
  /// 2. **Package Discovery**: Scan for local Swift packages and merge with existing configurations
  /// 3. **Target Processing**: Load app definitions and create target-specific configurations
  /// 4. **YAML Generation**: Create intermediate XcodeGen configuration files
  /// 5. **Project Creation**: Run XcodeGen to create the Xcode project
  /// 6. **Deployment Setup**: Generate CI/CD workflows and Fastlane configurations
  /// 7. **Cleanup & Launch**: Optionally clean up temporary files and open the project
  ///
  /// ## Error Handling
  ///
  /// The method can throw various errors during execution:
  /// - File I/O errors when reading/writing configuration files
  /// - YAML parsing errors for malformed configuration files
  /// - Package discovery errors for invalid Swift packages
  /// - Template rendering errors for malformed Stencil templates
  /// - XcodeGen execution errors for invalid project configurations
  ///
  /// ## Side Effects
  ///
  /// - Modifies global state in `DefaultNames.shared` and `FileGenerator.shared`
  /// - Creates directory structure for app targets
  /// - Generates multiple YAML configuration files
  /// - Executes external XcodeGen process
  /// - May open Xcode project automatically
  ///
  /// - Throws: Various errors related to file operations, YAML processing, or project generation
  public mutating func run() throws {
    // MARK: Phase 1: Initialize Global Configuration

    // Configure shared state that will be used throughout the generation process
    if let sharedFrameworkName {
      DefaultNames.shared.frameworkName = sharedFrameworkName
    }
    DefaultNames.shared.appsFolder = appsDir
    FileGenerator.shared.projectPath = projectPath

    // Copy binary dependencies (XcodeGen, etc.) to project's .bin directory
    try copyBinFiles()

    // MARK: Phase 2: Package Discovery and Dependency Resolution

    // Discover local Swift packages and merge with existing dependency configurations
    let extractedPackages = try extractPackageData()
    let updatedPackages = try mergePackageData(with: extractedPackages)
    let updatedDependencies = try mergeDependenciesData(with: updatedPackages, and: extractedPackages)
    let loadedBuildToolPlugins = try buildToolPluginsData()

    // MARK: Phase 3: Target Configuration Processing

    // Load app target definitions and filter if specific target requested
    let targets: Targets
    if let target {
      targets = try loadAppsData().filter { $0.key == target }
    } else {
      targets = try loadAppsData()
    }

    // Create directory structure and copy template files for each target
    try copyAppsTargetsFiles(with: targets)

    // Build target configurations and schemes for XcodeGen
    let appsTargets = buildAppsTargets(with: targets, and: updatedDependencies, and: loadedBuildToolPlugins)
    let appsTargetsSchemes = buildAppsTargetsSchemes(with: targets)

    // MARK: Phase 4: YAML Configuration Generation

    // Generate all intermediate YAML files required by XcodeGen
    try buildBaseYML()
    try buildTargetsYML(with: appsTargets, and: appsTargetsSchemes)
    try buildPackagesYML(with: updatedPackages)
    try buildDependenciesYML(with: updatedDependencies, and: loadedBuildToolPlugins)
    try buildProjectYML(with: updatedDependencies, and: loadedBuildToolPlugins)

    // MARK: Phase 5: Xcode Project Generation

    // Execute XcodeGen to create the actual .xcodeproj file
    runXcodeGen()

    // MARK: Phase 6: Cleanup and Project Launch

    // Optionally clean up intermediate files and open the generated project
    if !keepGeneratedYML {
      try deleteGeneratedYMLs()
    }
    if !doNotOpenProject {
      shell("open \(projectName).xcodeproj")
    }

    // MARK: Phase 7: Deployment Configuration Generation

    // Generate CI/CD workflows and Fastlane configurations
    try generateDeploymentData(with: targets)
  }

  // MARK: - File Operations and Setup

  /// Copies binary dependencies to the project's .bin directory.
  ///
  /// This method ensures that required build tools (like XcodeGen) are available
  /// in the project's local .bin directory, making the project self-contained
  /// and independent of system-wide tool installations.
  ///
  /// - Throws: File system errors if copying fails
  private func copyBinFiles() throws {
    try FileGenerator.shared.copyToBinFolder()
  }

  /// Configures the appropriate secrets generation script based on the selected method.
  ///
  /// This method ensures that only one secrets generation script is active at a time,
  /// replacing any conflicting script with the correct one based on the `secretsGenerationType` setting.
  ///
  /// ## Secrets Generation Methods
  ///
  /// - **Keychain**: Uses macOS Keychain for secure storage (recommended for local development)
  /// - **Keystore**: Uses encrypted file storage (recommended for CI/CD environments)
  ///
  /// - Note: This method modifies the `preBuildScripts` array in place
  private mutating func handleSecretsGenerationType() {
    switch secretsGenerationType {
    case .keychain:
      if let index = preBuildScripts.firstIndex(of: .generateSecretsKeystore) {
        preBuildScripts[index] = .generateSecretsKeychain
      }
    case .keystore:
      if let index = preBuildScripts.firstIndex(of: .generateSecretsKeychain) {
        preBuildScripts[index] = .generateSecretsKeystore
      }
    }
  }

  // MARK: - YAML Configuration Generation

  /// Generates the base.yml file containing target templates for XcodeGen.
  ///
  /// This method creates the foundational template definitions that will be used
  /// by all app targets. It configures different target types with their respective
  /// build scripts, dependencies, and settings.
  ///
  /// ## Generated Templates
  ///
  /// - **Application**: Main app target template with pre/post-build scripts
  /// - **Tests**: Unit test target template with test host configuration
  /// - **NotificationService**: Push notification extension template
  /// - **Framework**: Shared framework template (if using framework architecture)
  /// - **SwiftLintable**: Template for targets that should run SwiftLint
  ///
  /// ## Build Scripts Configuration
  ///
  /// The method automatically configures the appropriate secrets generation script
  /// based on the `secretsGenerationType` setting before creating templates.
  ///
  /// - Throws: YAML encoding errors or file writing errors
  private mutating func buildBaseYML() throws {
    handleSecretsGenerationType()

    let application = DefaultApplication(
      preBuildScripts: preBuildScripts.map(\.preScript),
      postBuildScripts: postBuildScripts.map(\.postScript),
      useFramework: useFramework
    ).build()
    let test = DefaultTest().build()
    let notificationService = DefaultAirshipNotificationService(postBuildScripts: [PostBuildScript.updateBuildNumber]).build()
    let framework = useFramework ? DefaultOGShared().build() : nil

    let swiftLintable = SwiftLintable(postCompileScripts: [.lint])

    let templates = Templates(
      application: application,
      tests: test,
      notificationService: notificationService,
      framework: framework,
      swiftLintable: swiftLintable
    )

    let encoder = YAMLEncoder()
    encoder.options = YAMLEncoder.Options(sortKeys: true)
    let targetTemplates = try encoder.encode(TargetTemplates(targetTemplates: templates))
    try targetTemplates.save(to: FileGenerator.shared.prependProjectPath(for: DefaultName.File.baseYML))
  }

  // MARK: - Data Loading and Processing

  /// Loads and parses the Apps.yml configuration file.
  ///
  /// This method reads the main configuration file that defines all app targets,
  /// their properties, build configurations, and deployment settings.
  ///
  /// ## Apps.yml Structure
  ///
  /// The file should contain a dictionary where keys are target identifiers
  /// and values are target configurations including:
  /// - App display name and version
  /// - Bundle identifiers for different configurations
  /// - Provisioning profiles and certificates
  /// - Firebase configurations
  ///
  /// - Returns: A `Targets` dictionary mapping target IDs to their configurations
  /// - Throws: File reading errors or YAML parsing errors for malformed configuration
  private func loadAppsData() throws -> Targets {
    let yamlDecoder = YAMLDecoder()
    let data = try String(contentsOfFile: FileGenerator.shared.prependProjectPath(for: appsYmlPath), encoding: .utf8)
    return try yamlDecoder.decode(Targets.self, from: data)
  }

  /// Creates the directory structure and template files for all app targets.
  ///
  /// This method sets up the complete file system structure for each app target,
  /// including source directories, configuration files, Firebase configurations,
  /// and test targets. It also handles copying initial template files if specified.
  ///
  /// ## Created Structure for Each Target
  ///
  /// ```
  /// Apps/
  /// ├── TargetName/
  /// │   ├── Config/
  /// │   ├── Firebase/
  /// │   │   ├── alpha/GoogleService-Info.plist
  /// │   │   ├── beta/GoogleService-Info.plist
  /// │   │   └── release/GoogleService-Info.plist
  /// │   ├── Resources/
  /// │   ├── Sources/
  /// │   │   └── CodeGenerated/Secrets.generated.swift
  /// │   ├── Info.plist
  /// │   ├── app.entitlements
  /// │   ├── .assetsFetcher.yml
  /// │   └── .phraseapp.yml
  /// └── TargetName-Tests/
  ///    └── Sources/TargetName-Tests.swift
  /// ```
  ///
  /// ## Template File Handling
  ///
  /// - If `initialFilesPath` is specified, copies all files from that directory
  /// - If `generateSwiftAppFile` is true, creates a basic SwiftUI app file
  /// - Creates shared source directory if not using framework architecture
  ///
  /// - Parameter targets: Dictionary of target configurations to process
  /// - Throws: File system errors during directory creation or file copying
  private func copyAppsTargetsFiles(with targets: Targets) throws {
    try targets.forEach {
      try FileGenerator.shared.createDir(at: "\(DefaultName.Folder.apps)/\($0.key)/Config")
      try FileGenerator.shared.createDir(at: "\(DefaultName.Folder.apps)/\($0.key)/Firebase")
      try FileGenerator.shared.createDir(at: "\(DefaultName.Folder.apps)/\($0.key)/Firebase/alpha")
      try FileGenerator.shared.copyFile(at: "\(DefaultName.Folder.apps)/\($0.key)/Firebase/GoogleService-Info-alpha.plist", toPath: "\(DefaultName.Folder.apps)/\($0.key)/Firebase/alpha/GoogleService-Info.plist")
      try FileGenerator.shared.createDir(at: "\(DefaultName.Folder.apps)/\($0.key)/Firebase/beta")
      try FileGenerator.shared.copyFile(at: "\(DefaultName.Folder.apps)/\($0.key)/Firebase/GoogleService-Info-beta.plist", toPath: "\(DefaultName.Folder.apps)/\($0.key)/Firebase/beta/GoogleService-Info.plist")
      try FileGenerator.shared.createDir(at: "\(DefaultName.Folder.apps)/\($0.key)/Firebase/release")
      try FileGenerator.shared.copyFile(at: "\(DefaultName.Folder.apps)/\($0.key)/Firebase/GoogleService-Info-release.plist", toPath: "\(DefaultName.Folder.apps)/\($0.key)/Firebase/release/GoogleService-Info.plist")
      try FileGenerator.shared.createDir(at: "\(DefaultName.Folder.apps)/\($0.key)/Resources")
      try FileGenerator.shared.createDir(at: "\(DefaultName.Folder.apps)/\($0.key)/Sources")
      try FileGenerator.shared.createFile(at: "\(DefaultName.Folder.apps)/\($0.key)/Info.plist", with: "defaultFiles/app/Info.plist")
      try FileGenerator.shared.createFile(at: "\(DefaultName.Folder.apps)/\($0.key)/app.entitlements", with: "defaultFiles/app/app.entitlements")
      try FileGenerator.shared.createFile(at: "\(DefaultName.Folder.apps)/\($0.key)/.assetsFetcher.yml", with: "defaultFiles/app/assetsFetcher.yml")
      try FileGenerator.shared.createFile(at: "\(DefaultName.Folder.apps)/\($0.key)/.phraseapp.yml", with: "defaultFiles/app/phraseapp.yml")
      try FileGenerator.shared.createFile(at: "\(DefaultName.Folder.apps)/\($0.key)/Sources/CodeGenerated/Secrets.generated.swift")
      try FileGenerator.shared.createDir(at: "\(DefaultName.Folder.apps)/\($0.key)-Tests/Sources")
      try FileGenerator.shared.createFile(at: "\(DefaultName.Folder.apps)/\($0.key)-Tests/Sources/\($0.key)-Tests.swift", with: "defaultFiles/tests/Tests.swift")
      try FileGenerator.shared.createFile(at: "\(DefaultName.Folder.apps)/\($0.key)-Tests/Info.plist")
      if let initialFilesPath {
        try FileGenerator.shared.copyInitialFiles(at: "\(DefaultName.Folder.apps)/\($0.key)", contentOfPath: initialFilesPath)
      } else if generateSwiftAppFile {
        try FileGenerator.shared.createInitialSwiftFileIfNeeded(at: "\(DefaultName.Folder.apps)/\($0.key)/Sources/", fileName: "MyAppView.swift", contentOfPath: "defaultFiles/app/MyAppView.swift")
      }
    }
    if !useFramework {
      try FileGenerator.shared.createDir(at: DefaultName.buildPath(names: [.Folder.shared, .Folder.sources]))
    }
  }

  // MARK: - Target Configuration Building

  /// Builds XcodeGen target configurations from the loaded app definitions.
  ///
  /// This method transforms the high-level app configurations from Apps.yml into
  /// detailed XcodeGen target specifications, handling dependency resolution,
  /// build tool plugins, and creating associated test and notification service targets.
  ///
  /// ## Target Types Created
  ///
  /// For each app target, this method creates:
  /// 1. **Main App Target**: The primary application target
  /// 2. **Test Target**: Unit test target with proper test host configuration
  /// 3. **Notification Service Target**: Push notification extension target
  ///
  /// ## Dependency Resolution
  ///
  /// - **Framework Architecture**: Apps depend on the shared framework target
  /// - **Source Architecture**: Apps directly include all project dependencies and build tool plugins
  ///
  /// ## Configuration Mapping
  ///
  /// Each target receives:
  /// - Display name, version, and executable name from Apps.yml
  /// - Build configurations (Alpha, Beta, Release) with appropriate settings
  /// - Provisioning profiles and bundle identifiers per configuration
  /// - Dependencies and build tool plugins based on architecture choice
  ///
  /// - Parameter targets: App target definitions from Apps.yml
  /// - Parameter projectDependencies: Resolved project dependencies (packages, SDKs, etc.)
  /// - Parameter projectBuildToolPlugins: Build tool plugins for code generation and linting
  /// - Returns: Dictionary mapping target names to their XcodeGen configurations
  private func buildAppsTargets(with targets: Targets, and projectDependencies: [Project.Target.Dependency], and projectBuildToolPlugins: [Project.Target.BuildToolPlugin]?) -> [String: AppTarget] {
    var dependencies = [AppTarget.Dependency]()
    var buildToolPlugins = [AppTarget.BuildToolPlugin]()
    if useFramework {
      dependencies = [AppTarget.Dependency(target: DefaultName.Target.ogShared)]
    } else {
      dependencies = projectDependencies.map {
        AppTarget.Dependency(sdk: $0.sdk, package: $0.package, product: $0.product, target: $0.target)
      }
      buildToolPlugins = projectBuildToolPlugins?.compactMap {
        AppTarget.BuildToolPlugin(package: $0.package, plugin: $0.plugin)
      } ?? []
    }
    var appTargets = [String: AppTarget]()
    for target in targets {
      appTargets[target.key] = AppTarget(
        templates: ["Application"],
        dependencies: dependencies + [AppTarget.Dependency(target: "\(target.key)-" + DefaultName.Target.notificationService)],
        settings: AppTarget.Settings(
          base: AppTarget.Base(
            displayName: target.value.name,
            marketingVersion: target.value.version,
            executableName: target.key,
            productName: target.key
          ),
          configs: target.value.filterProvisioningProfileSpecifier(for: DefaultName.Target.notificationService).configs
        ),
        buildToolPlugins: buildToolPlugins
      )
      appTargets[target.key + "-Tests"] = AppTarget(
        templates: ["Tests"],
        settings: AppTarget.Settings(
          base: AppTarget.Base(testHost: "$(BUILT_PRODUCTS_DIR)/\(target.key).app/\(target.key)")),
        templateAttributes: AppTarget.TemplateAttribute(targetID: target.key))
      let configs = target.value.provisioningProfileSpecifier(for: DefaultName.Target.notificationService).configs.mapValues {
        ConfigProperties(
          developmentTeam: $0.developmentTeam,
          productBundleIdentifier: $0.notificationServiceBundleIdentifier ?? $0.productBundleIdentifier + "." + DefaultName.Target.notificationService,
          codeSignIdentity: $0.codeSignIdentity,
          provisioningProfileSpecifier: $0.provisioningProfileSpecifier
        )
      }

      appTargets[target.key + "-" + DefaultName.Target.notificationService] = AppTarget(
        templates: [DefaultName.Target.notificationService],
        settings: AppTarget.Settings(
          base: AppTarget.Base(marketingVersion: target.value.version),
          configs: configs
        ))
    }
    return appTargets
  }

  /// Generates Xcode schemes for all app targets and configurations.
  ///
  /// This method creates comprehensive scheme configurations that enable developers
  /// to build, test, profile, analyze, and archive apps in different configurations
  /// directly from Xcode's scheme selector.
  ///
  /// ## Scheme Naming Convention
  ///
  /// Schemes are named using the pattern: `{TargetName} {Configuration}`
  /// - Example: "MyApp Alpha", "MyApp Beta", "MyApp Release"
  ///
  /// ## Scheme Components
  ///
  /// Each scheme includes:
  /// - **Build**: Defines which targets to build for different actions
  /// - **Run**: Configures app launch with appropriate configuration and Firebase debug settings
  /// - **Test**: Sets up unit test execution with proper test targets
  /// - **Profile**: Enables performance profiling in the specified configuration
  /// - **Analyze**: Configures static analysis with the build configuration
  /// - **Archive**: Sets up archiving for distribution
  ///
  /// ## Firebase Debug Configuration
  ///
  /// - Beta configuration enables Firebase debug mode for testing
  /// - Alpha and Release configurations disable Firebase debug mode
  ///
  /// - Parameter targets: App target definitions to create schemes for
  /// - Returns: Dictionary mapping scheme names to their configurations
  private func buildAppsTargetsSchemes(with targets: Targets) -> [String: AppTarget.Scheme] {
    var schemes = [String: AppTarget.Scheme]()
    for target in targets.sorted(by: { $0.key < $1.key }) {
      for config in Configuration.allCases.sorted(by: ({ $0.rawValue < $1.rawValue })) {
        let fbDebugDisabled = config != .beta
        let fbDebugEnabled = config == .beta
        schemes["\(target.key) \(config.rawValue)"] = AppTarget.Scheme(
          build: AppTarget.Build(targets: [
            target.key: [.run, .profile, .analyze, .archive],
            target.key + "-Tests": [.test]
          ]),
          run: AppTarget.Run(
            config: config,
            commandLineArguments:
            AppTarget.Run.CommandLineArgument(
              fbDebugEnabled: fbDebugEnabled,
              fbDebugDisabled: fbDebugDisabled
            )
          ),
          test: AppTarget.Test(
            targets: [target.key + "-Tests"],
            config: config
          ),
          profile: AppTarget.Profile(config: config),
          analyze: AppTarget.Analyze(config: config),
          archive: AppTarget.Archive(config: config))
      }
    }
    return schemes
  }

  /// Generates the targets.yml file containing all app target and scheme definitions.
  ///
  /// This method creates the XcodeGen configuration file that defines all the
  /// individual app targets, their dependencies, build settings, and associated
  /// Xcode schemes for different configurations.
  ///
  /// ## File Contents
  ///
  /// The generated targets.yml includes:
  /// - All app targets with their templates, dependencies, and settings
  /// - Test targets with proper test host configurations
  /// - Notification service targets for push notifications
  /// - Comprehensive scheme definitions for all configurations
  ///
  /// - Parameter appTargets: Dictionary of target configurations
  /// - Parameter schemes: Dictionary of scheme configurations
  /// - Throws: YAML encoding errors or file writing errors
  private func buildTargetsYML(with appTargets: [String: AppTarget], and schemes: [String: AppTarget.Scheme]) throws {
    let xcodeProject = AppTarget.XcodeProject(
      targets: appTargets,
      schemes: schemes
    )
    let encoder = YAMLEncoder()
    encoder.options = YAMLEncoder.Options(sortKeys: true)
    let data = try encoder.encode(xcodeProject)
    try data.save(to: FileGenerator.shared.prependProjectPath(for: DefaultName.File.targetsYML))
  }

  // MARK: - Deployment Configuration Generation

  /// Generates CI/CD deployment configurations using Stencil templates.
  ///
  /// This method creates the deployment automation files that enable continuous
  /// integration and deployment through GitHub Actions and Fastlane. It validates
  /// Firebase configurations and generates context-aware deployment scripts.
  ///
  /// ## Generated Files
  ///
  /// 1. **GitHub Actions Workflow** (`.github/workflows/main.yml`):
  ///   - Automated testing on pull requests
  ///   - Building and archiving for different configurations
  ///   - Deployment to TestFlight and App Store
  ///
  /// 2. **Fastlane Appfile** (`fastlane/Appfile`):
  ///   - Apple Developer account configuration
  ///   - Team and app identifier mappings
  ///
  /// 3. **Fastlane Fastfile** (`fastlane/Fastfile`):
  ///   - Build automation lanes for different configurations
  ///   - Certificate and provisioning profile management
  ///   - Upload and deployment automation
  ///
  /// ## Template Resolution
  ///
  /// - Uses custom templates from `templatesDir` if specified
  /// - Falls back to built-in default templates from the module bundle
  /// - Templates receive rich context including target configurations, Firebase settings, and build parameters
  ///
  /// ## Firebase Validation
  ///
  /// The method validates Firebase configurations by:
  /// - Checking that GoogleService-Info.plist files exist for all configurations
  /// - Verifying bundle identifiers match between Apps.yml and Firebase configurations
  /// - Extracting Google App IDs for deployment automation
  ///
  /// - Parameter targets: App target configurations with Firebase settings
  /// - Throws: Template resolution errors, Firebase validation errors, or file generation errors
  private func generateDeploymentData(with targets: Targets) throws {
    let model = OGDeploymentGeneratorModel(
      projectPath: projectPath,
      githubWorkflowsDir: githubWorkflowsDir,
      fastlaneDir: fastlaneDir,
      appsDir: DefaultName.Folder.apps,
      firebaseInfoPlistDir: firebaseInfoPlistDir,
      targets: targets
    )

    let templatesDirectory = (templatesDir.map { projectPath.withTrailingSlash() + $0 } ?? Bundle.module.resourcePath?.appendPathComponent("Templates"))
    guard let finalDirectory = templatesDirectory else {
      throw OGDeploymentGeneratorError.noTemplatesPath
    }

    let stencilHelper = StencilHelper(templatesDir: finalDirectory)

    try model.makeGenerationData().forEach {
      try stencilHelper.write(
        data: $0,
        projectName: projectName,
        appIconSetPath: appIconSetPath,
        appsDir: DefaultName.Folder.apps,
        useFramework: useFramework,
        hasPackagesTestHosts: !extractPackageData().isEmpty,
        xcodeVersion: xcodeVersion
      )
    }
  }

  // MARK: - Package Discovery and Management

  /// Discovers and extracts information from local Swift packages.
  ///
  /// This method scans the specified package directories to find all Swift packages
  /// and extracts their library names and paths for integration into the project.
  ///
  /// ## Discovery Process
  ///
  /// 1. Scans all directories specified in `packagesPath`
  /// 2. Recursively searches for Package.swift files
  /// 3. Parses each Package.swift to extract library definitions
  /// 4. Filters out test utilities libraries (ending with "TestsUtils")
  /// 5. Maps library names to their relative paths
  ///
  /// ## Package Integration
  ///
  /// Discovered packages are automatically:
  /// - Added to the packages.yml configuration
  /// - Included as dependencies in the dependencies.yml
  /// - Made available to all app targets (unless using framework architecture)
  ///
  /// - Returns: Dictionary mapping library names to their relative paths
  /// - Throws: File system errors or package parsing errors
  private func extractPackageData() throws -> [String: String] {
    var allPackageFolders: [String] = []

    // Iterate through all package paths and collect package folders from each
    for packagePath in packagesPath {
      let packageFolders = try findPackages(at: FileGenerator.shared.prependProjectPath(for: packagePath))
      allPackageFolders.append(contentsOf: packageFolders)
    }

    return try extractPackages(at: allPackageFolders)
  }

  /// Merges discovered local packages with existing package configurations.
  ///
  /// This method combines automatically discovered local packages with manually
  /// configured packages from the packages.yml file, giving priority to discovered
  /// packages to ensure the configuration stays up-to-date.
  ///
  /// ## Merge Strategy
  ///
  /// - Preserves existing remote packages (those without local paths)
  /// - Updates or adds local packages based on discovery results
  /// - Discovered packages override existing local packages with same names
  ///
  /// ## Package Types
  ///
  /// - **Local Packages**: Have file system paths and are discovered automatically
  /// - **Remote Packages**: External dependencies without paths (preserved as-is)
  ///
  /// - Parameter extractedPackages: Dictionary of discovered local packages
  /// - Returns: Merged package configuration combining existing and discovered packages
  /// - Throws: File reading errors or YAML parsing errors
  private func mergePackageData(with extractedPackages: [String: String]) throws -> [String: Package] {
    let yamlDecoder = YAMLDecoder()
    let packagesData = try String(contentsOfFile: FileGenerator.shared.prependProjectPath(for: packagesYml), encoding: .utf8)
    let currentPackages = try yamlDecoder.decode(Packages.self, from: packagesData).packages.compactMapValues {
      $0.path == nil ? $0 : nil
    }

    let packages = extractedPackages.compactMapValues {
      Package(path: $0)
    }

    return currentPackages.merging(packages) { _, new in new }
  }

  /// Generates the packages.yml file with merged package configurations.
  ///
  /// This method creates the XcodeGen packages configuration file that defines
  /// all Swift packages (both local and remote) that the project depends on.
  ///
  /// - Parameter packages: Merged package configurations to write
  /// - Throws: YAML encoding errors or file writing errors
  private func buildPackagesYML(with packages: [String: Package]) throws {
    let encoder = YAMLEncoder()
    encoder.options = YAMLEncoder.Options(sortKeys: true)
    let yamlPackaget = try encoder.encode(Packages(packages: packages))
    try yamlPackaget.save(to: FileGenerator.shared.prependProjectPath(for: packagesYml))
  }

  /// Merges existing dependencies with discovered packages to create final dependency list.
  ///
  /// This method combines manually configured dependencies from dependencies.yml
  /// with automatically discovered local packages, ensuring all required dependencies
  /// are available to the project targets.
  ///
  /// ## Dependency Resolution
  ///
  /// - Preserves existing dependencies that don't conflict with discovered packages
  /// - Adds new dependencies for all discovered local packages
  /// - Removes dependencies for packages that no longer exist
  /// - Sorts final list alphabetically for consistent output
  ///
  /// ## Dependency Types
  ///
  /// - **Package Dependencies**: Swift packages (local or remote)
  /// - **SDK Dependencies**: System frameworks and libraries
  /// - **Target Dependencies**: Internal project targets
  ///
  /// - Parameter packages: Merged package configurations
  /// - Parameter extractedPackages: Discovered local packages
  /// - Returns: Final list of project dependencies
  /// - Throws: File reading errors or YAML parsing errors
  private func mergeDependenciesData(with packages: [String: Package], and extractedPackages: [String: String]) throws -> [Project.Target.Dependency] {
    let dependenciesData = try String(contentsOfFile: FileGenerator.shared.prependProjectPath(for: dependenciesYml), encoding: .utf8)
    let yamlDecoder = YAMLDecoder()
    let currentDependencies = try yamlDecoder.decode(Dependencies.self, from: dependenciesData).dependencies.compactMap {
      packages[$0.package ?? ""] == nil && $0.package != nil ? nil : $0
    }

    let dependencies = extractedPackages.map {
      Project.Target.Dependency(package: $0.key)
    }
    return Array(Set(currentDependencies).union(Set(dependencies))).sorted { lhs, rhs in
      if let lhsPackage = lhs.package, let rhsPackage = rhs.package, let lhsProduct = lhs.product, let rhsProduct = rhs.product {
        return lhsPackage + lhsProduct < rhsPackage + rhsProduct
      }
      return lhs.package ?? lhs.sdk ?? lhs.target ?? "" < rhs.package ?? rhs.sdk ?? rhs.target ?? ""
    }
  }

  /// Loads build tool plugin configurations from dependencies.yml.
  ///
  /// Build tool plugins are Swift packages that provide build-time code generation
  /// and processing capabilities, such as SwiftGen for asset generation or
  /// SwiftLint for code quality enforcement.
  ///
  /// - Returns: Array of build tool plugin configurations, or nil if none defined
  /// - Throws: File reading errors or YAML parsing errors
  private func buildToolPluginsData() throws -> [Project.Target.BuildToolPlugin]? {
    let dependenciesData = try String(contentsOfFile: FileGenerator.shared.prependProjectPath(for: dependenciesYml), encoding: .utf8)
    let yamlDecoder = YAMLDecoder()
    return try yamlDecoder.decode(Dependencies.self, from: dependenciesData).buildToolPlugins
  }

  /// Generates the dependencies.yml file with updated dependencies and build tool plugins.
  ///
  /// This method creates the final dependencies configuration that will be used
  /// by XcodeGen to resolve all project dependencies and build tool plugins.
  ///
  /// - Parameter updatedDependencies: Final list of project dependencies
  /// - Parameter updateBuildToolPlugins: Build tool plugin configurations
  /// - Throws: YAML encoding errors or file writing errors
  private func buildDependenciesYML(with updatedDependencies: [Project.Target.Dependency], and updateBuildToolPlugins: [Project.Target.BuildToolPlugin]?) throws {
    let encoder = YAMLEncoder()
    encoder.options = YAMLEncoder.Options(sortKeys: true)
    let yamlDependencies = try encoder.encode(Dependencies(dependencies: updatedDependencies, buildToolPlugins: updateBuildToolPlugins))
    try yamlDependencies.save(to: FileGenerator.shared.prependProjectPath(for: dependenciesYml))
  }

  /// Generates the main project.yml file for XcodeGen.
  ///
  /// This method creates the root XcodeGen configuration file that defines the
  /// overall project structure, build settings, and global configurations.
  ///
  /// ## Project Configuration
  ///
  /// The generated project.yml includes:
  /// - Project name and basic metadata
  /// - Swift and iOS version requirements
  /// - Xcode and XcodeGen version constraints
  /// - Global dependencies and build tool plugins
  /// - Package path configurations
  /// - Framework vs. source architecture settings
  ///
  /// - Parameter updatedDependencies: Final project dependencies
  /// - Parameter updatedBuildToolPlugins: Build tool plugin configurations
  /// - Throws: YAML encoding errors or file writing errors
  private func buildProjectYML(with updatedDependencies: [Project.Target.Dependency], and updatedBuildToolPlugins: [Project.Target.BuildToolPlugin]?) throws {
    let encoder = YAMLEncoder()
    encoder.options = YAMLEncoder.Options(sortKeys: true)
    let project = DefaultProject(
      name: projectName,
      swiftVersion: swiftVersion,
      iOSVersion: iosVersion,
      xcodeVersion: xcodeVersion,
      minimumXcodeGenVersion: minimumXcodeGenVersion,
      dependencies: updatedDependencies,
      buildToolPlugins: updatedBuildToolPlugins,
      useFramework: useFramework,
      packagePaths: packagesPath
    ).build()
    let yamlProject = try encoder.encode(project)
    try yamlProject.save(to: FileGenerator.shared.prependProjectPath(for: DefaultName.File.projectYML))
  }

  // MARK: - Project Generation and Cleanup

  /// Executes XcodeGen to create the Xcode project from generated YAML configurations.
  ///
  /// This method runs the XcodeGen tool with caching enabled to generate the final
  /// .xcodeproj file from all the YAML configuration files created during the
  /// generation process.
  ///
  /// ## XcodeGen Process
  ///
  /// - Uses the local XcodeGen binary from .bin/bin/xcodegen
  /// - Enables caching (--use-cache) for faster subsequent generations
  /// - Processes project.yml as the main configuration file
  /// - Automatically includes base.yml, targets.yml, packages.yml, and dependencies.yml
  ///
  /// ## Error Handling
  ///
  /// If XcodeGen fails, the shell command will return a non-zero exit code,
  /// but this method doesn't throw. Check the console output for XcodeGen errors.
  private func runXcodeGen() {
    shell("./.bin/bin/xcodegen --use-cache")
  }

  /// Cleans up intermediate YAML files generated during project creation.
  ///
  /// This method removes the temporary YAML configuration files that were created
  /// for XcodeGen. These files are typically only needed during the generation
  /// process and can be safely deleted afterward.
  ///
  /// ## Deleted Files
  ///
  /// - project.yml: Main project configuration
  /// - base.yml: Target templates and base configurations
  /// - targets.yml: Individual target and scheme definitions
  ///
  /// ## Usage
  ///
  /// This method is called automatically unless the `--keep-generated-yml` flag
  /// is specified, which is useful for debugging project generation issues.
  ///
  /// - Throws: File system errors if deletion fails
  private func deleteGeneratedYMLs() throws {
    try FileManager.default.removeItem(atPath: FileGenerator.shared.prependProjectPath(for: DefaultName.File.projectYML))
    try FileManager.default.removeItem(atPath: FileGenerator.shared.prependProjectPath(for: DefaultName.File.baseYML))
    try FileManager.default.removeItem(atPath: FileGenerator.shared.prependProjectPath(for: DefaultName.File.targetsYML))
  }

  // MARK: - Utility Methods

  /// Executes shell commands and returns their output and exit status.
  ///
  /// This utility method provides a simple interface for running shell commands
  /// during the project generation process, such as executing XcodeGen or opening
  /// the generated Xcode project.
  ///
  /// ## Usage Examples
  ///
  /// ```swift
  /// // Execute XcodeGen
  /// shell("./.bin/bin/xcodegen --use-cache")
  ///
  /// // Open generated project
  /// shell("open MyProject.xcodeproj")
  /// ```
  ///
  /// ## Error Handling
  ///
  /// The method captures both stdout and stderr in the returned output string.
  /// Check the returned exit status to determine if the command succeeded:
  /// - 0: Success
  /// - Non-zero: Error (check output for details)
  ///
  /// - Parameter command: Shell command to execute
  /// - Returns: Tuple containing the command output (stdout + stderr) and exit status
  @discardableResult
  func shell(_ command: String) -> (String?, Int32) {
    let task = Process()

    task.launchPath = "/bin/bash"
    task.arguments = ["-c", command]

    let pipe = Pipe()
    task.standardOutput = pipe
    task.standardError = pipe
    task.launch()

    let data = pipe.fileHandleForReading.readDataToEndOfFile()
    let output = String(data: data, encoding: .utf8)
    task.waitUntilExit()
    return (output, task.terminationStatus)
  }

  // MARK: - Package Discovery Implementation

  /// Recursively searches for Swift Package.swift files in a directory.
  ///
  /// This method performs a deep search through the specified directory to find
  /// all Swift packages, excluding build artifacts and temporary directories.
  ///
  /// ## Search Behavior
  ///
  /// - Recursively searches all subdirectories by default
  /// - Excludes directories starting with ".build" (Swift build artifacts)
  /// - Returns full paths to Package.swift files
  /// - Returns empty array if the search path doesn't exist
  ///
  /// ## Parameters
  ///
  /// - Parameter path: Directory path to search for packages
  /// - Parameter infinitiveDepth: Whether to search recursively (currently unused, always recursive)
  /// - Returns: Array of full paths to Package.swift files
  /// - Throws: File system errors during directory traversal
  ///
  /// ## Implementation Note
  ///
  /// The `infinitiveDepth` parameter is currently unused and both code paths
  /// perform the same recursive search. This could be simplified in future versions.
  private func findPackages(at path: String, infinitiveDepth: Bool = true) throws -> [String] {
    guard FileManager.default.fileExists(atPath: path) else { return [] }
    let folder = path.components(separatedBy: "/").last ?? ""

    if infinitiveDepth {
      return try FileManager.default.subpathsOfDirectory(atPath: path)
        .filter { !$0.components(separatedBy: "/").contains { $0.hasPrefix(".build") } }
        .compactMap { $0.hasSuffix("Package.swift") ? "\(path)/\($0)" : nil }
    } else {
      return try FileManager.default.subpathsOfDirectory(atPath: path)
        .filter { !$0.components(separatedBy: "/").contains { $0.hasPrefix(".build") } }
        .compactMap { $0.hasSuffix("Package.swift") ? "\(path)/\($0)" : nil }
        .compactMap { $0.components(separatedBy: folder).count == 2 ? "\($0)" : nil }
    }
  }

  /// Extracts library information from Swift Package.swift files.
  ///
  /// This method parses Package.swift files to extract library definitions and
  /// their associated paths, creating a mapping that can be used for project
  /// dependency resolution.
  ///
  /// ## Parsing Process
  ///
  /// 1. **Content Normalization**: Removes whitespace and newlines for easier parsing
  /// 2. **Library Detection**: Uses regex to find `.library(...)` declarations
  /// 3. **Name Extraction**: Extracts library names from `name: "LibraryName"` patterns
  /// 4. **Path Calculation**: Converts absolute paths to relative paths from project root
  /// 5. **Filtering**: Excludes libraries ending with "TestsUtils" to focus on main libraries
  ///
  /// ## Library Selection Strategy
  ///
  /// - Only processes the first main library from each package
  /// - Completely ignores packages that only contain TestsUtils libraries
  /// - This ensures clean dependency graphs without test utilities cluttering the main app targets
  ///
  /// ## Regex Patterns Used
  ///
  /// - Library detection: `.library\\(.*?\\)` - Finds library declarations
  /// - Name extraction: `name:(.*?),` - Extracts the name parameter value
  ///
  /// ## Error Handling
  ///
  /// - Skips packages that can't be read or parsed
  /// - Continues processing other packages if individual packages fail
  /// - Returns partial results rather than failing completely
  ///
  /// - Parameter path: Array of full paths to Package.swift files
  /// - Returns: Dictionary mapping library names to their relative paths from project root
  /// - Throws: Regex compilation errors (rare) or severe file system errors
  private func extractPackages(at path: [String]) throws -> [String: String] {
    var libraryNames = [String: String]()
    for packagePath in path {
      guard FileManager.default.fileExists(atPath: packagePath) else { continue }
      var content = try String(contentsOfFile: packagePath, encoding: .utf8)
      content = String(content.compactMap { $0.isNewline || $0.isWhitespace ? nil : $0 })

      let range = NSRange(location: 0, length: content.utf16.count)
      let regexOptions: NSRegularExpression.Options = [.allowCommentsAndWhitespace]
      let libraryRegex = try NSRegularExpression(pattern: ".library\\(.*?\\)", options: regexOptions)
      let libraryResults = libraryRegex.matches(in: content, range: range).map {
        String(content[Range($0.range, in: content)!])
      }

      let libraryNameResults = try libraryResults.compactMap {
        let range = NSRange(location: 0, length: $0.utf16.count)
        let libraryNameRegex = try NSRegularExpression(pattern: "name:(.*?),", options: regexOptions)
        if let libraryNameResult = libraryNameRegex.firstMatch(in: $0, range: range) {
          return String($0[Range(libraryNameResult.range(at: 1), in: $0)!])
        } else {
          return nil
        }
      }
      let relativePackagePath = packagePath
        .replacingOccurrences(of: projectPath, with: "")
        .replacingOccurrences(of: "Package.swift", with: "")
        .dropFirst()

      let cleanedLibraryNames = libraryNameResults.map { $0.replacingOccurrences(of: "\"", with: "") }

      // Completely ignore TestsUtils libraries - only process main libraries
      let mainLibraryNames = cleanedLibraryNames.filter { !$0.hasSuffix("TestsUtils") }

      // Only add the first main library (ignore packages that only have TestsUtils)
      if let libraryName = mainLibraryNames.first {
        libraryNames[libraryName] = String(relativePackagePath)
      }
    }
    return libraryNames
  }
}

// MARK: OGDeploymentGenerator.OGDeploymentGeneratorError

/// Error types specific to the OGDeploymentGenerator.
///
/// These errors represent various failure conditions that can occur during
/// the project generation process.
extension OGDeploymentGenerator {
  /// Errors that can occur during deployment generation.
  ///
  /// - `noTemplatesPath`: Thrown when template directory cannot be resolved,
  ///  either from custom templates directory or built-in bundle resources.
  enum OGDeploymentGeneratorError: Error {
    case noTemplatesPath
  }
}

// MARK: OGDeploymentGenerator.SecretsGenerationType

/// Secrets generation method configuration.
///
/// Defines how app secrets should be generated and stored during the build process.
/// Different methods are suitable for different environments and security requirements.
extension OGDeploymentGenerator {
  /// Available secrets generation methods.
  ///
  /// - `keychain`: Uses macOS Keychain for secure storage (recommended for local development)
  /// - `keystore`: Uses encrypted file storage (recommended for CI/CD environments)
  enum SecretsGenerationType: String, ExpressibleByArgument {
    init?(argument: String) {
      self.init(rawValue: argument)
    }

    case keychain
    case keystore
  }
}

// MARK: OGDeploymentGenerator.ScriptEnum

/// Build script configuration enum.
///
/// Defines available pre-build and post-build scripts that can be executed
/// during the Xcode build process. These scripts handle various automation
/// tasks like secrets generation, build numbering, and deployment preparation.
extension OGDeploymentGenerator {
  /// Available build scripts for pre-build and post-build phases.
  ///
  /// ## Pre-Build Scripts
  /// - `generateSecretsKeychain`: Generates app secrets from macOS Keychain
  /// - `generateSecretsKeystore`: Generates app secrets from encrypted keystore
  /// - `generateUICatalog`: Creates UI component catalog from design tokens
  ///
  /// ## Post-Build Scripts
  /// - `updateBuildNumber`: Automatically increments build number
  /// - `copyGoogleServiceInfoPlist`: Copies appropriate Firebase configuration
  /// - `uploadSymbolsForCrashlytics`: Uploads debug symbols to Firebase Crashlytics
  enum ScriptEnum: String, ExpressibleByArgument {
    init?(argument: String) {
      self.init(rawValue: argument)
    }

    case generateSecretsKeychain
    case generateSecretsKeystore
    case updateBuildNumber
    case copyGoogleServiceInfoPlist
    case uploadSymbolsForCrashlytics
    case generateUICatalog

    /// Converts script enum to PostBuildScript configuration.
    ///
    /// - Returns: Corresponding PostBuildScript instance
    /// - Note: Only valid for post-build script types, crashes for pre-build scripts
    var postScript: PostBuildScript {
      switch self {
      case .updateBuildNumber:
        return PostBuildScript.updateBuildNumber
      case .copyGoogleServiceInfoPlist:
        return PostBuildScript.copyGoogleServiceInfoPlist
      case .uploadSymbolsForCrashlytics:
        return PostBuildScript.uploadSymbolsForCrashlytics
      default:
        fatalError("Script \(self) is not a valid post-build script")
      }
    }

    /// Converts script enum to PreBuildScript configuration.
    ///
    /// - Returns: Corresponding PreBuildScript instance
    /// - Note: Only valid for pre-build script types, crashes for post-build scripts
    var preScript: PreBuildScript {
      switch self {
      case .generateSecretsKeychain:
        return PreBuildScript.generateSecretsKeychain
      case .generateSecretsKeystore:
        return PreBuildScript.generateSecretsKeystore
      case .generateUICatalog:
        return PreBuildScript.generateUICatalog
      default:
        fatalError("Script \(self) is not a valid pre-build script")
      }
    }
  }
}

// MARK: - Developer Guidance and Best Practices

// # Developer Guidance for OGDeploymentGenerator
//
// ## Making Safe Modifications
//
// When modifying the OGDeploymentGenerator, follow these guidelines to ensure stability:
//
// ### 1. Understanding the Pipeline
//
// The generation process follows a strict pipeline. Understand the dependencies between phases:
// ```
// Configuration → Package Discovery → Target Processing → YAML Generation → Project Creation → Deployment Setup
// ```
//
// Changes in earlier phases affect all subsequent phases. Test thoroughly when modifying:
// - Package discovery logic (`extractPackageData`, `findPackages`, `extractPackages`)
// - Dependency resolution (`mergeDependenciesData`, `mergePackageData`)
// - Target building (`buildAppsTargets`, `buildAppsTargetsSchemes`)
//
// ### 2. Preserving Backward Compatibility
//
// - Always maintain existing command-line options and their behavior
// - Add new options with sensible defaults that don't break existing workflows
// - Use deprecation warnings before removing functionality
// - Test with existing Apps.yml configurations
//
// ### 3. Error Handling Best Practices
//
// - Provide clear, actionable error messages
// - Include file paths and line numbers when reporting YAML parsing errors
// - Fail fast for critical errors, but continue processing when possible
// - Log progress information to help users understand what's happening
//
// ## Adding New Features
//
// ### 1. Adding or Modifying Build Settings
//
// The OGDeploymentGenerator supports flexible build settings configuration through the Apps.yml file.
// Build settings flow through the system automatically via the Default* pattern implementations.
//
// #### How Build Settings Work
//
// Build settings can be added directly to your Apps.yml configuration and they will automatically
// appear in the generated Xcode project. This works because:
//
// 1. **Apps.yml Structure**: Your Apps.yml contains target configurations with nested config objects
// 2. **Automatic Mapping**: The `ConfigProperties` model  maps YAML keys to Xcode build settings
// 3. **Default Templates**: The Default* classes (like `DefaultApplication`) pass through all settings
// 4. **XcodeGen Integration**: XcodeGen processes these settings and applies them to the project
//
// #### Example: Adding a Custom Build Setting
//
// To add a custom build setting, you need to update both Apps.yml AND ConfigProperties:
//
// **Step 1: Add to Apps.yml**
// ```yaml
// # Apps.yml
// MyApp:
//  name: "My Awesome App"
//  version: "1.0.0"
//  configs:
//    Alpha:
//      productBundleIdentifier: "com.example.myapp.alpha"
//      ENABLE_BITCODE: "NO"
//      SWIFT_ACTIVE_COMPILATION_CONDITIONS: "ALPHA DEBUG"
//    Beta:
//      productBundleIdentifier: "com.example.myapp.beta"
//      ENABLE_BITCODE: "NO"
//      SWIFT_ACTIVE_COMPILATION_CONDITIONS: "BETA"
// ```
//
// **Step 2: Update ConfigProperties.swift**
// ```swift
// struct ConfigProperties: Codable {
//  // Existing properties...
//  let developmentTeam: String
//  let productBundleIdentifier: String
//
//  // Add new properties for your custom build settings
//  let enableBitcode: String?
//  let swiftActiveCompilationConditions: String?
//
//  enum CodingKeys: String, CodingKey {
//    // Existing keys...
//    case developmentTeam = "DEVELOPMENT_TEAM"
//    case productBundleIdentifier = "PRODUCT_BUNDLE_IDENTIFIER"
//
//    // Add CodingKeys for your custom build settings
//    case enableBitcode = "ENABLE_BITCODE"
//    case swiftActiveCompilationConditions = "SWIFT_ACTIVE_COMPILATION_CONDITIONS"
//  }
// }
// ```
//
// **Why Both Are Required:**
// - Apps.yml defines the values for each configuration
// - ConfigProperties provides the Swift property and CodingKey mapping
// - Without the ConfigProperties update, the build setting will be ignored during YAML processing
//
// #### Adding Custom Build Settings
//
// There are two approaches to add custom build settings:
//
// **Approach 1: Through ConfigProperties (Recommended)**
// 1. **Find the Setting Key**: Use Xcode's build settings editor to find the exact key name
// 2. **Add to Apps.yml**: Include the setting in your target's config section
// 3. **Update ConfigProperties**: Add the Swift property and CodingKey mapping (as shown in example above)
// 4. **Update Initializers**: Add the property to all ConfigProperties initializers
// 5. **Test Generation**: Run the generator and verify the setting appears in Xcode
//
// **Approach 2: Direct Passthrough (Limited Support)**
// Some build settings may work without ConfigProperties updates, but this is not guaranteed
// and may not work consistently across all configurations.
//
// #### Common Build Settings Examples
//
// ```yaml
// configs:
//  Release:
//    # Code signing
//    CODE_SIGN_IDENTITY: "iPhone Distribution"
//    PROVISIONING_PROFILE_SPECIFIER: "MyApp Release Profile"
//
//    # Deployment
//    IPHONEOS_DEPLOYMENT_TARGET: "15.0"
//    TARGETED_DEVICE_FAMILY: "1,2"  # iPhone and iPad
//
//    # Compiler settings
//    SWIFT_OPTIMIZATION_LEVEL: "-O"
//    SWIFT_COMPILATION_MODE: "wholemodule"
//
//    # App icons and assets
//    ASSETCATALOG_COMPILER_APPICON_NAME: "AppIcon"
//    ASSETCATALOG_COMPILER_ALTERNATE_APPICON_NAMES:
//      - "DarkIcon"
//      - "RetroIcon"
//
//    # Custom flags
//    OTHER_SWIFT_FLAGS: "-DRELEASE"
//    GCC_PREPROCESSOR_DEFINITIONS: "RELEASE=1"
// ```
//
// ### 2. Understanding the Default* Pattern
//
// The codebase uses a "Default*" naming pattern for classes that provide standard implementations
// of various project components. These are **not** hardcoded defaults but rather **customizable templates**
// that developers can modify or replace.
//
// #### Key Default* Classes
//
// - **`DefaultProject`**: Main project configuration (build settings, dependencies, package paths)
// - **`DefaultApplication`**: App target template (scripts, dependencies, framework usage)
// - **`DefaultTest`**: Test target template configuration
// - **`DefaultOGShared`**: Shared framework template (when using framework architecture)
// - **`DefaultAirshipNotificationService`**: Notification service extension template
//
// #### Why This Pattern Exists
//
// ```swift
// // Example: DefaultApplication provides a configurable template
// let application = DefaultApplication(
//  preBuildScripts: preBuildScripts.map(\.preScript),
//  postBuildScripts: postBuildScripts.map(\.postScript),
//  useFramework: useFramework
// ).build()
// ```
//
// This pattern allows:
// - **Customization**: Pass different parameters to change behavior
// - **Consistency**: Ensure all projects follow similar patterns
// - **Extensibility**: Easy to create new Default* classes for new target types
// - **Maintainability**: Centralized configuration logic
//
// #### Customizing Default* Implementations
//
// To modify default behavior:
//
// 1. **Locate the Default* Class**: Find the relevant class in `Models/TargetTemplates/`
// 2. **Understand the Builder Pattern**: Most use a `.build()` method that returns the final configuration
// 3. **Modify Parameters**: Change the initialization parameters or internal logic
// 4. **Test Thoroughly**: Ensure changes work across different project configurations
//
// #### Example: Customizing DefaultApplication
//
// ```swift
// // Current implementation in buildBaseYML()
// let application = DefaultApplication(
//  preBuildScripts: preBuildScripts.map(\.preScript),
//  postBuildScripts: postBuildScripts.map(\.postScript),
//  useFramework: useFramework
// ).build()
//
// // To add custom build settings to all apps:
// let application = DefaultApplication(
//  preBuildScripts: preBuildScripts.map(\.preScript),
//  postBuildScripts: postBuildScripts.map(\.postScript),
//  useFramework: useFramework,
//  customBuildSettings: [
//    "ENABLE_BITCODE": "NO",
//    "SWIFT_VERSION": swiftVersion
//  ]
// ).build()
// ```
//
// #### Creating New Default* Classes
//
// When adding new target types:
//
// 1. **Follow the Pattern**: Create `DefaultMyNewTarget` class
// 2. **Implement Builder**: Provide a `.build()` method returning the target configuration
// 3. **Accept Parameters**: Allow customization through initializer parameters
// 4. **Add to Templates**: Include in the `Templates` struct in `buildBaseYML()`
//
// ### 3. Build Settings Data Flow
//
// Understanding how build settings flow through the system helps when debugging or customizing:
//
// ```
// Apps.yml → ConfigProperties → AppTarget.Settings → XcodeGen → Xcode Project
// ```
//
// #### Detailed Flow
//
// 1. **Apps.yml Parsing**: YAML decoder creates target configurations with nested config objects
// 2. **ConfigProperties Mapping**: Each config (Alpha, Beta, Release) becomes a `ConfigProperties` instance
// 3. **Target Building**: `buildAppsTargets()` creates `AppTarget.Settings` with the config properties
// 4. **YAML Generation**: Settings are encoded into targets.yml for XcodeGen
// 5. **Project Generation**: XcodeGen reads targets.yml and applies settings to .xcodeproj
// 6. **Xcode Integration**: Xcode reads the project file and applies build settings
//
// #### Key Classes in the Flow
//
// - **`ConfigProperties`**: Represents build settings for a single configuration
// - **`AppTarget.Settings`**: Contains base settings and per-configuration overrides
// - **`AppTarget.Base`**: Common settings applied to all configurations
// - **`TargetTemplates`**: Template definitions that provide default build settings
//
// #### Debugging Build Settings
//
// To debug build setting issues:
//
// 1. **Check Apps.yml**: Verify the setting is correctly defined in YAML
// 2. **Inspect Generated YAML**: Use `--keep-generated-yml` to examine targets.yml
// 3. **Verify XcodeGen Output**: Check that XcodeGen processes the setting correctly
// 4. **Examine Xcode Project**: Open the .xcodeproj and verify the setting appears
//
// #### Build Setting Precedence
//
// Build settings are applied in this order (later overrides earlier):
//
// 1. **Template Defaults**: From Default* classes (lowest priority)
// 2. **Base Settings**: From `AppTarget.Base` (applies to all configurations)
// 3. **Configuration Settings**: From Apps.yml config sections (highest priority)
//
// #### How ConfigProperties Handles Build Settings
//
// The `ConfigProperties` model handles build settings through explicit property mapping.
// For a build setting to work through ConfigProperties, it needs both a Swift property
// and a corresponding CodingKey mapping:
//
// ```swift
// // ConfigProperties.swift - Simplified structure
// struct ConfigProperties: Codable {
//  // Known properties with explicit mappings
//  let developmentTeam: String
//  let productBundleIdentifier: String
//  let customBuildSetting: String?  // Example custom property
//
//  enum CodingKeys: String, CodingKey {
//    case developmentTeam = "DEVELOPMENT_TEAM"
//    case productBundleIdentifier = "PRODUCT_BUNDLE_IDENTIFIER"
//    case customBuildSetting = "MY_CUSTOM_BUILD_SETTING"
//    // ... other mappings
//  }
// }
// ```
//
// **How This Works:**
//
// 1. **Property Declaration**: Swift property holds the value from Apps.yml
// 2. **CodingKey Mapping**: Maps the Swift property name to the Xcode build setting key
// 3. **YAML Decoding**: Codable reads the build setting from Apps.yml into the Swift property
// 4. **YAML Encoding**: When generating targets.yml, the CodingKey determines the output key name
// 5. **XcodeGen Processing**: XcodeGen recognizes the build setting key and applies it to the project
//
// #### Adding Support for New Build Settings in ConfigProperties
//
// To add support for a new build setting that needs special handling:
//
// 1. **Add Property**: Add a new property to the ConfigProperties struct
// ```swift
// let myNewSetting: String?  // or [String]? for arrays, Bool? for booleans
// ```
//
// 2. **Add CodingKey**: Map it to the exact Xcode build setting name
// ```swift
// enum CodingKeys: String, CodingKey {
//  case myNewSetting = "MY_XCODE_BUILD_SETTING_KEY"
//  // ... existing keys
// }
// ```
//
// 3. **Update All Initializers**: Add the property to every ConfigProperties initializer
// ```swift
// init(developmentTeam: String, productBundleIdentifier: String, myNewSetting: String? = nil, ...) {
//  self.developmentTeam = developmentTeam
//  self.productBundleIdentifier = productBundleIdentifier
//  self.myNewSetting = myNewSetting
//  // ... other properties
// }
// ```
//
// 4. **Update Helper Methods**: Add the property to methods like `filterProvisioningProfileSpecifier` and `provisioningProfileSpecifier`
// 5. **Test**: Verify the setting appears correctly in the generated Xcode project
//
// #### Alternative: Direct Build Setting Passthrough
//
// For build settings that don't need special handling, you can add them directly to Apps.yml
// without modifying ConfigProperties. XcodeGen will pass through any valid Xcode build setting:
//
// ```yaml
// configs:
//  Release:
//    # These work without ConfigProperties modifications
//    ENABLE_BITCODE: "NO"
//    SWIFT_ACTIVE_COMPILATION_CONDITIONS: "RELEASE"
//    OTHER_LDFLAGS: "-ObjC"
// ```
//
// This works because the YAML structure is preserved through the generation pipeline.
//
// ### 4. New Build Scripts
//
// To add a new build script:
//
// 1. Add the case to `ScriptEnum`
// 2. Implement the corresponding `PreBuildScript` or `PostBuildScript`
// 3. Update the `preScript` or `postScript` computed property
// 4. Add the script to default arrays if it should be enabled by default
// 5. Update command-line help text
//
// ### 5. New Target Types
//
// To support new target types (e.g., widgets, extensions):
//
// 1. Create new template builders in the Models/TargetTemplates directory
// 2. Add the template to the `Templates` struct in `buildBaseYML()`
// 3. Update `buildAppsTargets()` to create instances of the new target type
// 4. Add appropriate scheme configurations in `buildAppsTargetsSchemes()`
//
// ### 6. New Configuration Options
//
// When adding new command-line options:
//
// 1. Add the `@Option` or `@Flag` property with comprehensive help text
// 2. Use the option in the appropriate pipeline phase
// 3. Update the file header documentation with usage examples
// 4. Consider whether the option should affect template rendering
//
// ## Common Pitfalls to Avoid
//
// ### 1. File Path Handling
//
// - Always use `FileGenerator.shared.prependProjectPath()` for relative paths
// - Be careful with path separators on different platforms
// - Handle missing files gracefully with clear error messages
//
// ### 2. YAML Processing
//
// - Always sort keys when encoding YAML for consistent output
// - Handle optional values properly to avoid null entries
// - Validate YAML structure before processing
//
// ### 3. Template Rendering
//
// - Ensure all template variables are provided in the context
// - Handle missing templates gracefully
// - Validate template output before writing files
//
// ### 4. Package Discovery
//
// - Don't assume Package.swift files are well-formed
// - Handle regex compilation errors
// - Be careful with relative path calculations
//
// ### 5. Dependency Resolution
//
// - Avoid circular dependencies between packages
// - Handle missing dependencies gracefully
// - Ensure dependency order is deterministic
//
// ## Performance Considerations
//
// - Package discovery can be slow for large directory trees
// - YAML parsing and generation can be memory-intensive for large configurations
// - Template rendering should be cached when possible
// - Consider parallel processing for independent operations
//
// ## Debugging Tips
//
// - Use `--keep-generated-yml` to inspect intermediate YAML files
// - Enable verbose logging for package discovery
// - Check XcodeGen output for project generation errors
// - Validate generated files manually before automation
//
// ## Architecture Evolution
//
// When making significant architectural changes:
//
// 1. **Maintain the Pipeline**: The current pipeline architecture works well and should be preserved
// 2. **Extract Complexity**: Move complex logic into separate classes/structs
// 3. **Improve Testability**: Make methods more focused and testable
// 4. **Document Changes**: Update this documentation when making architectural changes
// 5. **Consider Migration**: Provide migration paths for existing configurations
